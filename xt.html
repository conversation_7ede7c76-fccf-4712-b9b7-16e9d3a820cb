<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دردشة المعلمين</title>

    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Firebase -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.3.1/firebase-app.js";
        import { getFirestore, collection, addDoc, updateDoc, deleteDoc, doc, onSnapshot, orderBy, query, serverTimestamp, where, getDocs, getDoc } from "https://www.gstatic.com/firebasejs/11.3.1/firebase-firestore.js";
        import { getStorage, ref, uploadBytes, getDownloadURL } from "https://www.gstatic.com/firebasejs/11.3.1/firebase-storage.js";

        // إعدادات Firebase مشفرة
        const encryptedConfig = "QUl6YVN5RGNwYmR1TTRDYVAwMDRlMDM3Ym9PajI5V1ROV3RqR2xJfGNoYXQtYWU0ZjMuZmlyZWJhc2VhcHAuY29tfGNoYXQtYWU0ZjN8Y2hhdC1hZTRmMy5maXJlYmFzZXN0b3JhZ2UuYXBwfDEyNTY1OTQ2ODUwNXwxOjEyNTY1OTQ2ODUwNTp3ZWI6MWYxMjAzNDYyOTZhYmZlZDI0YTRjOHxHLVJUU1lDMlBETEg=";

        // فك تشفير الإعدادات
        function decodeConfig(encoded) {
            const decoded = atob(encoded);
            const parts = decoded.split('|');
            return {
                apiKey: parts[0],
                authDomain: parts[1],
                projectId: parts[2],
                storageBucket: parts[3],
                messagingSenderId: parts[4],
                appId: parts[5],
                measurementId: parts[6]
            };
        }

        const firebaseConfig = decodeConfig(encryptedConfig);

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        const storage = getStorage(app);
        let selectedMessageId = null;
        let currentGroupId = null;

        document.addEventListener("DOMContentLoaded", () => {
            checkLoginStatus();
            loadMessages();
            loadGroups();
            loadFriendRequests();
            setupNavigation();
            updateRequestCount();
        });

        function checkLoginStatus() {
            const username = localStorage.getItem("username");
            const phone = localStorage.getItem("phone");

            if (username && phone) {
                showPage("home-container");
                document.querySelector(".navigation").style.display = "flex";
            } else {
                showPage("login-container");
                document.querySelector(".navigation").style.display = "none";
            }
        }

        function showPage(pageId) {
            const pages = ["login-container", "chat-container", "settings-container", "profile-container", "home-container", "group-container", "group-chat-container", "search-container", "friend-requests-container"];
            pages.forEach(page => {
                document.getElementById(page).style.display = page === pageId ? "block" : "none";
            });
        }

        function setupNavigation() {
            const navLinks = document.querySelectorAll('.nav-link');
            const indicator = document.querySelector('.nav-indicator');
            
            function updateIndicator(el) {
                indicator.style.width = `${el.offsetWidth}px`;
                indicator.style.left = `${el.offsetLeft}px`;
            }
            
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));
                    
                    // Add active class to clicked link
                    link.classList.add('active');
                    
                    // Update indicator position
                    updateIndicator(link);
                    
                    // Show the corresponding page
                    const pageId = link.getAttribute('data-page');
                    showPage(pageId);
                });
            });
            
            // Set initial active state
            const currentPage = document.querySelector('.container:not([style*="display: none"])');
            if (currentPage) {
                const pageId = currentPage.id;
                const activeLink = document.querySelector(`.nav-link[data-page="${pageId}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                    updateIndicator(activeLink);
                }
            }
        }

        document.getElementById("login-btn").addEventListener("click", () => {
            const username = document.getElementById("username").value.trim();
            const phone = document.getElementById("phone").value.trim();
            const profilePic = document.getElementById("profile-pic").files[0];

            if (username === "" || phone === "") {
                alert("الرجاء إدخال اسم المستخدم ورقم الهاتف");
                return;
            }

            localStorage.setItem("username", username);
            localStorage.setItem("phone", phone);

            if (profilePic) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    localStorage.setItem(`profilePic_${username}`, e.target.result);
                };
                reader.readAsDataURL(profilePic);
            }

            showPage("home-container");
            document.querySelector(".navigation").style.display = "flex";
        });

        document.getElementById("logout-btn").addEventListener("click", () => {
            localStorage.clear();
            showPage("login-container");
            document.querySelector(".navigation").style.display = "none";
        });

        document.getElementById("send-btn").addEventListener("click", async () => {
            const messageText = document.getElementById("message").value.trim();
            if (messageText === "") return;

            await addDoc(collection(db, "messages"), {
                sender: localStorage.getItem("username"),
                phone: localStorage.getItem("phone"),
                content: messageText,
                timestamp: serverTimestamp()
            });

            document.getElementById("message").value = "";
        });

        function loadMessages() {
            const messagesDiv = document.getElementById("messages");
            const q = query(collection(db, "messages"), orderBy("timestamp"));

            onSnapshot(q, (snapshot) => {
                messagesDiv.innerHTML = "";
                snapshot.forEach((docSnapshot) => {
                    const data = docSnapshot.data();
                    const msgDiv = document.createElement("div");
                    msgDiv.className = `message ${data.sender === localStorage.getItem("username") ? 'sent' : 'received'}`;

                    const userProfile = document.createElement("div");
                    userProfile.className = "user-profile";
                    userProfile.innerHTML = `<div class="profile-pic">${getProfilePic(data.sender)}</div><div class="username">${data.sender}</div>`;
                    userProfile.onclick = () => showProfile(data);

                    const msgContent = document.createElement("div");
                    msgContent.className = "msg-content";
                    if (data.content) {
                        msgContent.innerText = data.content;
                    } else if (data.audioUrl) {
                        msgContent.innerHTML = `<audio controls src="${data.audioUrl}"></audio>`;
                    }

                    msgDiv.appendChild(userProfile);
                    msgDiv.appendChild(msgContent);

                    if (data.sender === localStorage.getItem("username")) {
                        msgDiv.onclick = () => showMessageOptions(docSnapshot.id, data);
                    }

                    messagesDiv.appendChild(msgDiv);
                });
            });
        }

        function getProfilePic(username) {
            const profilePic = localStorage.getItem(`profilePic_${username}`);
            if (profilePic) {
                return `<img src="${profilePic}" alt="Profile Picture">`;
            } else {
                return `<div class="default-pic">${getInitials(username)}</div>`;
            }
        }

        function getInitials(name) {
            return name.charAt(0).toUpperCase();
        }

        function showProfile(userData) {
            document.getElementById("profile-name").innerText = userData.sender;
            document.getElementById("profile-phone").innerText = userData.phone;
            document.getElementById("profile-pic-display").innerHTML = getProfilePic(userData.sender);
            showPage("profile-container");
        }

        function showMessageOptions(messageId, data) {
            selectedMessageId = messageId;
            document.getElementById("popup-overlay").style.display = "block";
            const messageOptions = document.getElementById("message-options");
            messageOptions.style.display = "block";
            messageOptions.style.animation = "popupAppear 0.3s ease forwards";
        }

        document.getElementById("close-options").addEventListener("click", () => {
            const messageOptions = document.getElementById("message-options");
            messageOptions.style.animation = "popupDisappear 0.3s ease forwards";
            
            setTimeout(() => {
                messageOptions.style.display = "none";
                document.getElementById("popup-overlay").style.display = "none";
            }, 300);
        });

        // إغلاق النافذة عند النقر على الخلفية
        document.getElementById("popup-overlay").addEventListener("click", () => {
            document.getElementById("close-options").click();
        });

        // منع إغلاق النافذة عند النقر داخلها
        document.getElementById("message-options").addEventListener("click", (e) => {
            e.stopPropagation();
        });

        document.getElementById("delete-message-btn").addEventListener("click", async () => {
            await deleteDoc(doc(db, "messages", selectedMessageId));
            document.getElementById("message-options").style.display = "none";
        });

        document.getElementById("edit-message-btn").addEventListener("click", () => {
            const modal = document.getElementById("edit-message-modal");
            const messageRef = doc(db, "messages", selectedMessageId);
            
            // Get the current message content
            getDoc(messageRef).then((doc) => {
                if (doc.exists()) {
                    const textarea = document.getElementById("edit-message-text");
                    textarea.value = doc.data().content;
                    modal.classList.add("show");
                    modal.style.display = "block";
                    textarea.focus();
                }
            });

            document.getElementById("message-options").style.display = "none";
            document.getElementById("popup-overlay").style.display = "none";
        });

        document.getElementById("save-edit-btn").addEventListener("click", async () => {
            const newMessage = document.getElementById("edit-message-text").value.trim();
            const modal = document.getElementById("edit-message-modal");
            
            if (newMessage) {
                const button = document.getElementById("save-edit-btn");
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                button.disabled = true;
                
                try {
                    await updateDoc(doc(db, "messages", selectedMessageId), { content: newMessage });
                    button.innerHTML = '<i class="fas fa-check"></i> تم الحفظ';
                    setTimeout(() => {
                        modal.classList.remove("show");
                        setTimeout(() => {
                            modal.style.display = "none";
                            button.innerHTML = '<i class="fas fa-check"></i> <span>حفظ التغييرات</span>';
                            button.disabled = false;
                        }, 300);
                    }, 1000);
                } catch (error) {
                    button.innerHTML = '<i class="fas fa-times"></i> حدث خطأ';
                    button.disabled = false;
                }
            }
        });

        document.getElementById("cancel-edit-btn").addEventListener("click", () => {
            const modal = document.getElementById("edit-message-modal");
            modal.classList.remove("show");
            setTimeout(() => {
                modal.style.display = "none";
            }, 300);
        });

        document.getElementById("edit-message-modal").addEventListener("click", (e) => {
            if (e.target.id === "edit-message-modal") {
                const modal = document.getElementById("edit-message-modal");
                modal.classList.remove("show");
                setTimeout(() => {
                    modal.style.display = "none";
                }, 300);
            }
        });

        document.getElementById("back-to-chat-btn").addEventListener("click", () => {
            showPage("chat-container");
        });

        document.getElementById("change-profile-pic").addEventListener("click", () => {
            document.getElementById("profile-pic").click();
        });

        document.getElementById("profile-pic").addEventListener("change", (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    localStorage.setItem(`profilePic_${localStorage.getItem("username")}`, e.target.result);
                    document.getElementById("profile-pic-display").innerHTML = `<img src="${e.target.result}" alt="Profile Picture">`;
                };
                reader.readAsDataURL(file);
            }
        });

        // Add file handling
        document.getElementById("file-input").addEventListener("change", async (e) => {
            const files = e.target.files;
            for (let file of files) {
                await sendFileMessage(file);
            }
        });

        async function sendFileMessage(file) {
            const storageRef = ref(storage, `fileMessages/${Date.now()}_${file.name}`);
            await uploadBytes(storageRef, file);
            const fileUrl = await getDownloadURL(storageRef);

            await addDoc(collection(db, "messages"), {
                sender: localStorage.getItem("username"),
                phone: localStorage.getItem("phone"),
                fileUrl: fileUrl,
                fileName: file.name,
                timestamp: serverTimestamp()
            });
        }

        // Add user search functionality
        document.getElementById("search-btn").addEventListener("click", () => {
            showPage("search-container");
            searchUsers(); // Load all users initially
        });

        document.getElementById("search-user-btn").addEventListener("click", () => {
            const searchTerm = document.getElementById("user-search").value.toLowerCase();
            searchUsers(searchTerm); // Search with term
        });

        async function searchUsers(searchTerm = '') {
            const userList = document.getElementById("user-list");
            userList.innerHTML = ""; // Clear previous results

            const usersQuery = searchTerm ? 
                query(collection(db, "users"), where("username", "==", searchTerm)) :
                query(collection(db, "users"));

            const querySnapshot = await getDocs(usersQuery);

            if (querySnapshot.empty) {
                userList.innerHTML = "<p>لا يوجد مستخدمين</p>";
                return;
            }

            querySnapshot.forEach((doc) => {
                const userData = doc.data();
                const userItem = document.createElement("div");
                userItem.className = "user-item";
                userItem.innerHTML = `
                    <div class="user-info">
                        <div class="profile-pic">${getProfilePic(userData.username)}</div>
                        <div class="username">${userData.username}</div>
                    </div>
                `;
                
                const requestButton = createRequestButton(userData.username); // تغيير من doc.id إلى username
                userItem.appendChild(requestButton);
                userList.appendChild(userItem);
            });
        }

        function createRequestButton(userId) {
            const button = document.createElement('button');
            button.className = 'send-request-btn';
            button.innerHTML = `
                <i class="fas fa-user-plus"></i>
                <span>إرسال طلب صداقة</span>
            `;
            
            // Add ripple effect
            button.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                ripple.className = 'ripple';
                this.appendChild(ripple);
                
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = e.clientX - rect.left - size/2 + 'px';
                ripple.style.top = e.clientY - rect.top - size/2 + 'px';
                
                // Remove ripple after animation
                ripple.addEventListener('animationend', () => {
                    ripple.remove();
                });

                // Change button state
                this.classList.add('sent');
                this.innerHTML = `
                    <i class="fas fa-check"></i>
                    <span>تم الإرسال</span>
                `;

                // Send friend request
                sendFriendRequest(userId);
            });

            return button;
        }

        async function sendFriendRequest(toUsername) {
            try {
                const fromUsername = localStorage.getItem("username");
                
                // التحقق من وجود طلب صداقة مسبق
                const existingRequestQuery = query(
                    collection(db, "friendRequests"),
                    where("from", "==", fromUsername),
                    where("to", "==", toUsername)
                );
                
                const existingRequestSnapshot = await getDocs(existingRequestQuery);
                if (!existingRequestSnapshot.empty) {
                    alert("لقد قمت بإرسال طلب صداقة مسبقاً");
                    return;
                }

                // إرسال طلب الصداقة
                await addDoc(collection(db, "friendRequests"), {
                    from: fromUsername,
                    to: toUsername,
                    timestamp: serverTimestamp()
                });
                
                alert("تم إرسال طلب الصداقة بنجاح");
            } catch (error) {
                console.error("Error sending friend request:", error);
                alert("حدث خطأ أثناء إرسال طلب الصداقة");
            }
        }

        // Load friend requests
        document.getElementById("mail-btn").addEventListener("click", () => {
            showPage("friend-requests-container");
            loadFriendRequests();
        });

        document.getElementById("close-friend-requests-btn").addEventListener("click", () => {
            showPage("home-container");
        });

        function loadFriendRequests() {
            const inboxDiv = document.getElementById("friend-requests-list");
            const currentUsername = localStorage.getItem("username");
            const q = query(
                collection(db, "friendRequests"), 
                where("to", "==", currentUsername),
                orderBy("timestamp", "desc")
            );

            onSnapshot(q, (snapshot) => {
                inboxDiv.innerHTML = "";
                if (snapshot.empty) {
                    inboxDiv.innerHTML = `
                        <div class="no-requests">
                            <i class="far fa-envelope-open"></i>
                            لا توجد طلبات صداقة جديدة
                        </div>`;
                    return;
                }

                snapshot.forEach((docSnapshot) => {
                    const data = docSnapshot.data();
                    const requestDiv = document.createElement("div");
                    requestDiv.className = "request-item animate-in"; // إضافة كلاس للتحريك
                    requestDiv.innerHTML = `
                        <div class="request-info">
                            <div class="profile-pic">${getProfilePic(data.from)}</div>
                            <div class="username">${data.from}</div>
                        </div>
                        <div class="request-actions">
                            <button class="accept-request-btn" data-id="${docSnapshot.id}">
                                <i class="fas fa-check"></i>
                                قبول
                            </button>
                            <button class="reject-request-btn" data-id="${docSnapshot.id}">
                                <i class="fas fa-times"></i>
                                رفض
                            </button>
                        </div>
                    `;
                    inboxDiv.appendChild(requestDiv);

                    // تحريك العناصر عند إزالتها
                    requestDiv.addEventListener('click', function(e) {
                        if (e.target.matches('.accept-request-btn, .reject-request-btn')) {
                            requestDiv.classList.add('animate-out');
                        }
                    });
                });

                // إضافة مستمعي الأحداث للأزرار
                document.querySelectorAll(".accept-request-btn").forEach(button => {
                    button.addEventListener("click", (e) => {
                        const requestId = e.target.getAttribute("data-id");
                        const requestItem = e.target.closest('.request-item');
                        requestItem.classList.add('fade-out');
                        setTimeout(() => acceptFriendRequest(requestId), 300);
                    });
                });

                document.querySelectorAll(".reject-request-btn").forEach(button => {
                    button.addEventListener("click", (e) => {
                        const requestId = e.target.getAttribute("data-id");
                        const requestItem = e.target.closest('.request-item');
                        requestItem.classList.add('fade-out');
                        setTimeout(() => rejectFriendRequest(requestId), 300);
                    });
                });
            });
        }

        async function acceptFriendRequest(requestId) {
            try {
                const requestDoc = await getDoc(doc(db, "friendRequests", requestId));
                const requestData = requestDoc.data();

                // إضافة إلى مجموعة الأصدقاء
                await addDoc(collection(db, "friends"), {
                    user1: requestData.from,
                    user2: requestData.to,
                    timestamp: serverTimestamp()
                });

                // حذف طلب الصداقة بدون إظهار إشعار
                await deleteDoc(doc(db, "friendRequests", requestId));
                
                // تحديث العرض مباشرة
                loadFriendRequests();
            } catch (error) {
                console.error("Error accepting friend request:", error);
            }
        }

        async function rejectFriendRequest(requestId) {
            try {
                // حذف طلب الصداقة بدون إظهار إشعار
                await deleteDoc(doc(db, "friendRequests", requestId));
                
                // تحديث العرض مباشرة
                loadFriendRequests();
            } catch (error) {
                console.error("Error rejecting friend request:", error);
            }
        }

        async function loadFriends() {
            const friendsList = document.getElementById("friends-list");
            const currentUserId = localStorage.getItem("userId");
            const q = query(collection(db, "friends"), where("user1", "==", currentUserId), or(where("user2", "==", currentUserId)));

            onSnapshot(q, (snapshot) => {
                friendsList.innerHTML = "";
                snapshot.forEach((docSnapshot) => {
                    const data = docSnapshot.data();
                    const friendId = data.user1 === currentUserId ? data.user2 : data.user1;
                    const friendItem = document.createElement("div");
                    friendItem.className = "friend-item";
                    friendItem.innerHTML = `
                        <div class="username">${friendId}</div>
                        <button class="start-chat-btn" data-id="${friendId}">بدء الدردشة</button>
                    `;
                    friendsList.appendChild(friendItem);
                });

                document.querySelectorAll(".start-chat-btn").forEach(button => {
                    button.addEventListener("click", (e) => {
                        const friendId = e.target.getAttribute("data-id");
                        startPrivateChat(friendId);
                    });
                });
            });
        }

        // Add private chat functionality
        async function startPrivateChat(otherUserId) {
            const chatId = getChatId(localStorage.getItem("userId"), otherUserId);
            // Switch to private chat view and load messages
        }

        function getChatId(uid1, uid2) {
            return [uid1, uid2].sort().join('_');
        }

        // Add group creation functionality
        document.getElementById("create-group-btn").addEventListener("click", () => {
            const groupName = prompt("أدخل اسم المجموعة:");
            const groupPassword = prompt("أدخل كلمة مرور للمجموعة (اختياري):");
            if (groupName) {
                createGroup(groupName, groupPassword);
            }
        });

        async function createGroup(groupName, groupPassword) {
            const groupId = generateGroupId();
            await addDoc(collection(db, "groups"), {
                name: groupName,
                id: groupId,
                password: groupPassword || null,
                creator: localStorage.getItem("username"),
                timestamp: serverTimestamp()
            });
            alert(`تم إنشاء المجموعة بنجاح. معرف المجموعة: ${groupId}`);
        }

        function generateGroupId() {
            return 'group_' + Math.random().toString(36).substr(2, 9);
        }

        // Load groups
        function loadGroups() {
            const groupList = document.getElementById("group-list");
            const q = query(collection(db, "groups"), orderBy("timestamp"));

            onSnapshot(q, (snapshot) => {
                groupList.innerHTML = "";
                snapshot.forEach((docSnapshot) => {
                    const data = docSnapshot.data();
                    const groupItem = document.createElement("div");
                    groupItem.className = "group-item";
                    groupItem.innerHTML = `
                        ${data.name} (ID: ${data.id})
                        ${data.creator === localStorage.getItem("username") ? '<button class="delete-group-btn" data-id="' + docSnapshot.id + '">حذف</button>' : ''}
                    `;
                    groupItem.onclick = () => joinGroup(data.id, data.password);
                    groupList.appendChild(groupItem);
                });

                document.querySelectorAll(".delete-group-btn").forEach(button => {
                    button.addEventListener("click", async (e) => {
                        e.stopPropagation();
                        const groupId = e.target.getAttribute("data-id");
                        await deleteGroup(groupId);
                    });
                });
            });
        }

        async function deleteGroup(groupId) {
            await deleteDoc(doc(db, "groups", groupId));
            alert("تم حذف المجموعة");
        }

        async function joinGroup(groupId, groupPassword) {
            if (groupPassword) {
                const enteredPassword = prompt("أدخل كلمة مرور المجموعة:");
                if (enteredPassword !== groupPassword) {
                    alert("كلمة المرور غير صحيحة");
                    return;
                }
            }
            currentGroupId = groupId;
            loadGroupMessages(groupId);
            showPage("group-chat-container");
        }

        async function loadGroupMessages(groupId) {
            const groupMessagesDiv = document.getElementById("group-messages");
            const q = query(collection(db, `groups/${groupId}/messages`), orderBy("timestamp"));

            onSnapshot(q, (snapshot) => {
                groupMessagesDiv.innerHTML = "";
                snapshot.forEach((docSnapshot) => {
                    const data = docSnapshot.data();
                    const msgDiv = document.createElement("div");
                    msgDiv.className = `message ${data.sender === localStorage.getItem("username") ? 'sent' : 'received'}`;

                    const userProfile = document.createElement("div");
                    userProfile.className = "user-profile";
                    userProfile.innerHTML = `<div class="profile-pic">${getProfilePic(data.sender)}</div><div class="username">${data.sender}</div>`;

                    const msgContent = document.createElement("div");
                    msgContent.className = "msg-content";
                    if (data.content) {
                        msgContent.innerText = data.content;
                    } else if (data.audioUrl) {
                        msgContent.innerHTML = `<audio controls src="${data.audioUrl}"></audio>`;
                    }

                    msgDiv.appendChild(userProfile);
                    msgDiv.appendChild(msgContent);

                    groupMessagesDiv.appendChild(msgDiv);
                });
            });
        }

        document.getElementById("send-group-message-btn").addEventListener("click", async () => {
            const messageText = document.getElementById("group-message").value.trim();
            if (messageText === "") return;

            await addDoc(collection(db, `groups/${currentGroupId}/messages`), {
                sender: localStorage.getItem("username"),
                phone: localStorage.getItem("phone"),
                content: messageText,
                timestamp: serverTimestamp()
            });

            document.getElementById("group-message").value = "";
        });

        // Update friend request count
        function updateRequestCount() {
            const currentUsername = localStorage.getItem("username");
            const q = query(
                collection(db, "friendRequests"),
                where("to", "==", currentUsername)
            );

            onSnapshot(q, (snapshot) => {
                const count = snapshot.size;
                const notification = document.querySelector('.btn-notification');
                notification.textContent = count;
                notification.style.display = count > 0 ? 'flex' : 'none';
            });
        }

        // Add these event listeners after your DOMContentLoaded event
        document.getElementById("home-chat-btn").addEventListener("click", () => {
            showPage("chat-container");
        });

        document.getElementById("home-groups-btn").addEventListener("click", () => {
            showPage("group-container");
        });

        document.getElementById("home-settings-btn").addEventListener("click", () => {
            showPage("settings-container");
        });

        // تحديث دالة البحث
        document.getElementById("search-user-btn").addEventListener("click", () => {
            const button = document.getElementById("search-user-btn");
            const loading = document.getElementById("loading-users");
            
            // إضافة تأثير النقر
            const ripple = document.createElement("div");
            ripple.className = "btn-ripple";
            button.appendChild(ripple);
            
            // إظهار التحميل
            loading.style.display = "flex";
            
            // تنفيذ البحث
            const searchTerm = document.getElementById("user-search").value.toLowerCase();
            searchUsers(searchTerm);
            
            // إزالة تأثير النقر بعد الانتهاء
            setTimeout(() => ripple.remove(), 600);
        });

        // إضافة زر العودة
        document.getElementById("back-to-home").addEventListener("click", () => {
            showPage("home-container");
        });

    </script>

    <style>
        /* التصميم العام */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            height: 100vh;
            color: #333;
            overflow: hidden;
        }

        .container, .chat, .settings, .profile, .home-container, .group-container, .group-chat-container, .search-container, .friend-requests-container {
            background-color: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            width: 100%;
            height: 100vh;
            text-align: center;
            color: #333;
            display: none;
            animation: fadeIn 0.5s ease;
            z-index: 1; /* Ensure these elements are above the background */
        }

        h2 {
            margin-bottom: 20px;
            color: #333;
        }

        input[type="text"], input[type="file"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
            background-color: rgba(255, 255, 255, 0.8);
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus, input[type="file"]:focus {
            border-color: #007bff;
        }

        button {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: #fff;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        button:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }

        .messages {
            height: calc(100vh - 260px);
            overflow-y: auto;
            margin-bottom: 60px;
            padding-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.8);
        }

        .message {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 10px;
            animation: fadeIn 0.5s ease;
            max-width: 80%;
            word-wrap: break-word;
            transition: transform 0.3s ease;
        }

        .message:hover {
            transform: scale(1.02);
        }

        .message.sent {
            background-color: #007bff;
            color: #fff;
            align-self: flex-end;
            margin-left: auto;
            animation: slideInRight 0.5s ease;
        }

        .message.received {
            background-color: #e9ecef;
            color: #333;
            align-self: flex-start;
            margin-right: auto;
            animation: slideInLeft 0.5s ease;
        }

        .user-profile {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .profile-pic {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #007bff;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin-right: 10px;
        }

        .profile-pic img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
        }

        .username {
            font-weight: bold;
        }

        .msg-content {
            margin-top: 5px;
        }

        .popup {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            color: #333;
            animation: fadeIn 0.5s ease;
        }

        .popup h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .popup p {
            margin-bottom: 20px;
            color: #666;
        }

        .popup button {
            margin: 5px;
        }

        .navigation {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to right, #2c3e50, #3498db);
            display: flex;
            justify-content: space-around;
            padding: 12px 0;
            box-shadow: 0 -2px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .nav-link {
            position: relative;
            text-decoration: none;
            color: rgba(255,255,255,0.7);
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.3s ease;
            padding: 8px 20px;
            border-radius: 12px;
            overflow: hidden;
        }

        .nav-icon {
            position: relative;
            font-size: 24px;
            margin-bottom: 4px;
            z-index: 1;
        }

        .nav-text {
            font-size: 12px;
            font-weight: 500;
            transform: translateY(0);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .nav-tooltip {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%) scale(0);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
            white-space: nowrap;
        }

        .nav-link:hover .nav-tooltip {
            transform: translateX(-50%) scale(1);
            opacity: 1;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            transition: all 0.3s ease;
            z-index: 0;
        }

        .nav-link:hover::before {
            transform: translate(-50%, -50%) scale(1);
        }

        .nav-link:hover {
            color: white;
        }

        .nav-link:hover .nav-text {
            transform: translateY(-2px);
            opacity: 1;
        }

        .nav-link:hover i {
            animation: bounce 0.5s ease;
        }

        .nav-link.active {
            color: white;
        }

        .nav-link.active::before {
            background: rgba(255,255,255,0.2);
            transform: translate(-50%, -50%) scale(1);
        }

        .nav-indicator {
            position: absolute;
            bottom: 0;
            height: 3px;
            border-radius: 3px;
            background: #fff;
            transition: all 0.3s ease;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        /* Add responsive styles */
        @media (max-width: 480px) {
            .nav-text {
                display: none;
            }
            
            .nav-link {
                padding: 8px 15px;
            }
            
            .nav-icon {
                font-size: 20px;
                margin-bottom: 0;
            }
        }

        .message-input-container {
            position: fixed;
            bottom: 60px;
            left: 0;
            right: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: white;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            animation: slideInUp 0.5s ease;
        }

        .message-input-container input {
            flex: 1;
            transition: border-color 0.3s ease;
        }

        .message-input-container input:focus {
            border-color: #007bff;
        }

        .message-input-container button {
            width: auto;
            padding: 10px 15px;
        }

        .message-options {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            min-width: 300px;
            max-width: 90%;
            backdrop-filter: blur(10px);
            animation: popupAppear 0.3s ease forwards;
        }

        .message-options.show {
            display: block;
        }

        .message-options h3 {
            color: #2c3e50;
            font-size: 20px;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
            padding-bottom: 10px;
        }

        .message-options h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 3px;
        }

        .message-options-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        #delete-message-btn, #edit-message-btn, #close-options {
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
        }

        #delete-message-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }

        #edit-message-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }

        #close-options {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .message-options button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .message-options button:active {
            transform: translateY(0);
        }

        .message-options button i {
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .message-options button:hover i {
            transform: scale(1.2);
        }

        /* تأثير الخلفية المعتمة */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 999;
            animation: fadeIn 0.3s ease;
        }

        /* التحريكات */
        @keyframes popupAppear {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        @keyframes popupDisappear {
            from {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            to {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 480px) {
            .message-options {
                width: 90%;
                padding: 20px;
            }
            
            .message-options button {
                padding: 10px 15px;
                font-size: 14px;
            }
        }

        .profile-pic-upload {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background-color: #007bff;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
            margin: 0 auto 20px;
            transition: background-color 0.3s ease;
        }

        .profile-pic-upload:hover {
            background-color: #0056b3;
        }

        .profile-pic-upload i {
            pointer-events: none;
        }

        #profile-pic {
            display: none;
        }

        .home-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
            padding: 30px 20px;
        }

        .home-container h2 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 40px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .home-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .home-btn {
            position: relative;
            display: flex;
            align-items: center;
            padding: 20px;
            font-size: 18px;
            border-radius: 12px;
            background: white;
            color: #2c3e50;
            border: none;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .home-btn i {
            font-size: 24px;
            margin-right: 15px;
            transition: transform 0.3s ease;
        }

        .home-btn span {
            flex: 1;
            text-align: right;
        }

        .btn-effect {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .home-btn:hover .btn-effect {
            transform: translateX(100%);
        }

        .home-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .home-btn:hover i {
            transform: scale(1.2);
        }

        .chat-btn { background: linear-gradient(45deg, #4CAF50, #45a049); color: white; }
        .groups-btn { background: linear-gradient(45deg, #2196F3, #1976D2); color: white; }
        .search-btn { background: linear-gradient(45deg, #9C27B0, #7B1FA2); color: white; }
        .requests-btn { background: linear-gradient(45deg, #FF9800, #F57C00); color: white; }
        .settings-btn { background: linear-gradient(45deg, #607D8B, #455A64); color: white; }

        .btn-notification {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4444;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7);
            }
            70% {
                transform: scale(1.1);
                box-shadow: 0 0 0 10px rgba(255, 68, 68, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(255, 68, 68, 0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .home-buttons {
                grid-template-columns: 1fr;
            }
            
            .home-btn {
                padding: 15px;
                font-size: 16px;
            }
            
            .home-btn i {
                font-size: 20px;
            }
        }

        /* Add loading animation */
        .loading-animation {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .search-container {
            margin-bottom: 20px;
        }

        .user-list {
            margin-top: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .user-item {
            background-color: #ffffff;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .user-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }

        .attachment-options {
            display: flex;
            gap: 10px;
        }

        #file-input {
            display: none;
        }

        .inbox {
            margin-top: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .request-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }

        .request-item:hover {
            background-color: #f1f1f1;
        }

        .request-item .username {
            flex: 1;
        }

        .request-item button {
            margin-left: 10px;
            transition: background-color 0.3s ease;
        }

        .request-item button:hover {
            background-color: #007bff;
            color: #fff;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .group-container {
            display: none;
            padding: 20px;
            background: white;
            height: 100vh;
        }

        .group-list {
            margin-top: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .group-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .group-item:hover {
            background-color: #f1f1f1;
        }

        .group-chat-container {
            display: none;
            padding: 20px;
            background: white;
            height: 100vh;
        }

        .group-messages {
            height: calc(100vh - 260px);
            overflow-y: auto;
            margin-bottom: 60px;
            padding-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.8);
        }

        .group-message-input-container {
            position: fixed;
            bottom: 60px;
            left: 0;
            right: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: white;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            animation: slideInUp 0.5s ease;
        }

        .group-message-input-container input {
            flex: 1;
            transition: border-color 0.3s ease;
        }

        .group-message-input-container input:focus {
            border-color: #007bff;
        }

        .group-message-input-container button {
            width: auto;
            padding: 10px 15px;
        }

        .friend-requests-container {
            display: none;
            padding: 20px;
            background: white;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto; /* Enable scrolling */
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        }

        .friend-requests-list {
            margin-top: 20px;
            margin-bottom: 80px; /* Add space for the close button */
            padding: 10px;
        }

        #close-friend-requests-btn {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: calc(100% - 40px);
            max-width: 400px;
            z-index: 1000; /* Ensure button stays on top */
        }

        .search-container, .friend-requests-container {
            background-color: #ffffff;
            height: 100vh;
            padding: 20px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
        }

        .search-container h2, .friend-requests-container h2 {
            color: #333;
            margin-bottom: 25px;
            text-align: center;
            font-size: 24px;
        }

        #user-search {
            width: calc(100% - 20px);
            padding: 12px;
            margin: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        #user-search:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }

        #search-user-btn {
            width: calc(100% - 20px);
            margin: 10px;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        #search-user-btn:hover {
            background-color: #0056b3;
        }

        .user-list, .friend-requests-list {
            margin-top: 20px;
            padding: 10px;
        }

        .user-item, .request-item {
            background-color: #ffffff;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .user-item .profile-pic, .request-item .profile-pic {
            width: 40px;
            height: 40px;
            margin-right: 15px;
        }

        .send-request-btn {
            padding: 10px 20px;
            border-radius: 25px;
            border: none;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-width: 130px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .send-request-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.25);
            background: linear-gradient(45deg, #0056b3, #004494);
        }

        .send-request-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .send-request-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s ease-out, height 0.6s ease-out;
        }

        .send-request-btn:active::before {
            width: 200px;
            height: 200px;
            opacity: 0;
        }

        .send-request-btn.sent {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            pointer-events: none;
        }

        .send-request-btn.sent i {
            animation: checkmark 0.8s ease-in-out forwards;
        }

        @keyframes checkmark {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .accept-request-btn, .reject-request-btn {
            padding: 8px 15px;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .accept-request-btn {
            background-color: #28a745;
            color: white;
            margin-right: 10px;
        }

        .reject-request-btn {
            background-color: #dc3545;
            color: white;
        }

        #close-friend-requests-btn {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: calc(100% - 40px);
            max-width: 400px;
            padding: 12px;
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        #close-friend-requests-btn:hover {
            background-color: #5a6268;
        }

        .home-buttons {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
        }

        .home-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px;
            font-size: 18px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .home-btn:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }

        .home-btn i {
            font-size: 20px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .ripple {
            position: absolute;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            pointer-events: none;
            transform: scale(0);
            animation: ripple 0.6s linear;
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .no-requests {
            text-align: center;
            color: #666;
            padding: 20px;
            font-style: italic;
        }

        .request-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .request-actions {
            display: flex;
            gap: 10px;
        }

        .request-item {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: transform 0.2s ease;
        }

        .request-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* تعديل الحاويات لتتناسب مع شريط التنقل */
        .container, .chat, .settings, .profile, .home-container, 
        .group-container, .group-chat-container, .search-container, 
        .friend-requests-container {
            padding-bottom: 80px; /* مساحة لشريط التنقل */
            height: calc(100vh - 60px); /* ارتفاع الصفحة ناقص ارتفاع شريط التنقل */
            overflow-y: auto;
        }

        /* تحديث موضع حاوية إدخال الرسائل */
        .message-input-container,
        .group-message-input-container {
            position: fixed;
            bottom: 70px; /* زيادة المسافة عن شريط التنقل */
            left: 0;
            right: 0;
            background: white;
            padding: 10px;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 998; /* أقل من شريط التنقل */
        }

        /* تحديث موضع زر الإغلاق في صفحة طلبات الصداقة */
        #close-friend-requests-btn {
            position: fixed;
            bottom: 80px; /* زيادة المسافة عن شريط التنقل */
            left: 50%;
            transform: translateX(-50%);
            width: calc(100% - 40px);
            max-width: 400px;
            z-index: 998;
        }

        /* تعديل شريط التنقل */
        .navigation {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px; /* تحديد ارتفاع ثابت */
            background: linear-gradient(to right, #2c3e50, #3498db);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 8px 0;
            box-shadow: 0 -2px 15px rgba(0,0,0,0.2);
            z-index: 999; /* جعله دائماً في المقدمة */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* تحديث المسافات في الرسائل */
        .messages,
        .group-messages {
            margin-bottom: 80px; /* مساحة لحقل الإدخال */
            height: calc(100vh - 260px);
        }

        /* تعديل الـ popup والـ message options */
        .popup,
        .message-options {
            z-index: 1000; /* أعلى من شريط التنقل */
        }

        /* تحسين التمرير في الصفحات */
        .container {
            -webkit-overflow-scrolling: touch; /* تمرير سلس على iOS */
            overflow-y: auto;
        }

        /* إضافة مساحة إضافية في نهاية المحتوى */
        .content-wrapper {
            padding-bottom: 80px;
        }

        /* تحديث أسلوب التمرير */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .search-container {
            background: linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%);
            padding: 20px;
            padding-top: 40px;
        }

        .search-container h2 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .search-container h2 i {
            font-size: 28px;
            color: #3498db;
            animation: bounce 2s infinite;
        }

        .search-box-wrapper {
            max-width: 600px;
            margin: 0 auto 30px;
            padding: 0 20px;
        }

        .search-box {
            background: white;
            border-radius: 15px;
            padding: 5px;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .search-box:focus-within {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .search-icon {
            padding: 0 15px;
            color: #3498db;
            font-size: 18px;
        }

        #user-search {
            flex: 1;
            border: none;
            padding: 15px;
            font-size: 16px;
            background: transparent;
        }

        #user-search:focus {
            outline: none;
        }

        #search-user-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        #search-user-btn:hover {
            transform: translateY(-2px);
            background: linear-gradient(45deg, #2980b9, #2573a7);
        }

        .btn-ripple {
            position: absolute;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        .loading-animation {
            display: none;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .user-list {
            max-width: 600px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .user-item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .user-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .user-item:hover::before {
            transform: translateX(100%);
        }

        .user-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .profile-pic {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            transition: transform 0.3s ease;
        }

        .user-item:hover .profile-pic {
            transform: scale(1.1);
        }

        .username {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .floating-btn {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #3498db;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            border: none;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            background: #2980b9;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسين للأجهزة المحمولة */
        @media (max-width: 480px) {
            .search-box {
                flex-direction: column;
                padding: 10px;
            }

            #search-user-btn {
                width: 100%;
                margin-top: 10px;
            }

            .user-item {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .user-info {
                flex-direction: column;
                gap: 10px;
            }
        }

        .search-input-container {
            position: relative;
            width: 100%;
            max-width: 500px; /* تقليل العرض الأقصى */
            margin: 0 auto;
            padding: 20px;
        }

        #user-search {
            width: 100%;
            height: 55px; /* تقليل الارتفاع */
            padding: 10px 55px;
            font-size: 16px; /* تقليل حجم الخط */
            border: 2px solid #e0e0e0;
            border-radius: 27px;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-align: center; /* توسيط النص */
            direction: rtl; /* اتجاه الكتابة من اليمين لليسار */
        }

        #user-search::placeholder {
            color: #95a5a6;
            font-size: 16px;
            opacity: 0.7;
            text-align: center; /* توسيط نص placeholder */
        }

        #search-user-btn {
            position: absolute;
            left: 25px;
            top: 50%;
            transform: translateY(-50%);
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* تحسين للأجهزة المحمولة */
        @media (max-width: 480px) {
            .search-input-container {
                padding: 10px;
            }

            #user-search {
                height: 50px;
                font-size: 14px;
                padding: 10px 50px;
            }

            #user-search::placeholder {
                font-size: 14px;
            }

            #search-user-btn {
                width: 40px;
                height: 40px;
            }
        }

        /* تحسين صفحة طلبات الصداقة */
        .friend-requests-container {
            background: linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%);
            padding: 20px;
            padding-top: 40px;
        }

        .friend-requests-container h2 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            position: relative;
        }

        .friend-requests-container h2 i {
            font-size: 32px;
            color: #3498db;
            animation: bellRing 2s infinite;
        }

        @keyframes bellRing {
            0%, 100% { transform: rotate(0); }
            10%, 30%, 50%, 70% { transform: rotate(10deg); }
            20%, 40%, 60% { transform: rotate(-10deg); }
            80%, 90% { transform: rotate(0); }
        }

        .friend-requests-list {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .request-item {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            transform: translateY(0);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .request-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .request-item:hover::before {
            transform: translateX(100%);
        }

        .request-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .request-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .request-info .profile-pic {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            transition: transform 0.3s ease;
        }

        .request-item:hover .profile-pic {
            transform: scale(1.1) rotate(360deg);
            transition: transform 0.6s ease;
        }

        .request-info .username {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .request-actions {
            display: flex;
            gap: 10px;
        }

        .accept-request-btn, .reject-request-btn {
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .accept-request-btn {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
        }

        .reject-request-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }

        .accept-request-btn:hover, .reject-request-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .accept-request-btn:active, .reject-request-btn:active {
            transform: translateY(0);
        }

        .accept-request-btn i, .reject-request-btn i {
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .accept-request-btn:hover i {
            transform: scale(1.2);
            animation: bounceCheck 0.5s ease infinite;
        }

        .reject-request-btn:hover i {
            transform: scale(1.2);
            animation: rotateCross 0.5s ease infinite;
        }

        @keyframes bounceCheck {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.3); }
        }

        @keyframes rotateCross {
            0% { transform: rotate(0); }
            100% { transform: rotate(180deg); }
        }

        .no-requests {
            text-align: center;
            color: #95a5a6;
            font-size: 18px;
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px;
            animation: fadeInUp 0.5s ease;
        }

        .no-requests i {
            font-size: 48px;
            color: #bdc3c7;
            margin-bottom: 15px;
            display: block;
        }

        #close-friend-requests-btn {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 500;
            margin-top: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            /* تعديل موضع وحجم الزر */
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 50%; /* تحديد العرض بنسبة 50% */
            max-width: 200px; /* تحديد أقصى عرض */
            min-width: 150px; /* تحديد أدنى عرض */
            /* إزالة التأثيرات الحركية */
            transition: background-color 0.3s ease;
        }

        #close-friend-requests-btn:hover {
            background: linear-gradient(45deg, #2c3e50, #2c3e50);
        }

        /* تحسين للأجهزة المحمولة */
        @media (max-width: 480px) {
            #close-friend-requests-btn {
                width: 70%; /* زيادة العرض على الشاشات الصغيرة */
                min-width: 200px;
            }
        }

        /* تحسين للأجهزة المحمولة */
        @media (max-width: 480px) {
            .request-item {
                flex-direction: column;
                text-align: center;
                padding: 15px;
            }

            .request-info {
                flex-direction: column;
                margin-bottom: 15px;
            }

            .request-actions {
                width: 100%;
                justify-content: center;
            }

            .accept-request-btn, .reject-request-btn {
                padding: 10px 20px;
                font-size: 14px;
            }
        }

        /* إضافة تحريكات جديدة لطلبات الصداقة */
        .animate-in {
            animation: slideIn 0.3s ease-out forwards;
        }

        .animate-out {
            animation: slideOut 0.3s ease-out forwards;
        }

        .fade-out {
            animation: fadeOut 0.3s ease-out forwards;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(20px);
            }
        }

        @keyframes fadeOut {
            to {
                opacity: 0;
                transform: scale(0.9);
            }
        }

        /* تحسينات صفحة الدردشة */
        .chat {
            background: linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%);
        }

        #chat-title {
            position: relative;
            padding: 20px;
            color: #2c3e50;
            font-size: 24px;
            text-align: center;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            animation: slideDown 0.5s ease;
        }

        #chat-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 3px;
        }

        .messages {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: calc(100vh - 300px);
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .message {
            max-width: 80%;
            margin: 15px 0;
            position: relative;
            animation: messageAppear 0.3s ease;
        }

        .message.sent {
            margin-left: auto;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 20px 5px 20px 20px;
            padding: 15px;
            color: white;
            box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
            transform-origin: right;
        }

        .message.received {
            margin-right: auto;
            background: white;
            border-radius: 5px 20px 20px 20px;
            padding: 15px;
            color: #2c3e50;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transform-origin: left;
        }

        .user-profile {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .profile-pic {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            background: linear-gradient(45deg, #3498db, #2980b9);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .message:hover .profile-pic {
            transform: scale(1.1);
        }

        .username {
            font-size: 14px;
            font-weight: 600;
            color: #7f8c8d;
        }

        .msg-content {
            font-size: 15px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message-input-container {
            background: white;
            border-radius: 25px;
            padding: 10px 20px;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            animation: slideUp 0.5s ease;
        }

        .attachment-options {
            color: #95a5a6;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .attachment-options:hover {
            color: #3498db;
        }

        #message {
            flex: 1;
            border: none;
            padding: 12px;
            font-size: 15px;
            border-radius: 20px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        #message:focus {
            outline: none;
            background: white;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
        }

        #send-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
        }

        #send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        #send-btn i {
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        #send-btn:hover i {
            animation: sendBounce 0.5s ease infinite;
        }

        /* التحريكات */
        @keyframes messageAppear {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes sendBounce {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(3px); }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تحسين التمرير */
        .messages::-webkit-scrollbar {
            width: 6px;
        }

        .messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .messages::-webkit-scrollbar-thumb {
            background: #bdc3c7;
            border-radius: 10px;
            transition: background 0.3s ease;
        }

        .messages::-webkit-scrollbar-thumb:hover {
            background: #95a5a6;
        }

        /* تحسين للأجهزة المحمولة */
        @media (max-width: 480px) {
            .message {
                max-width: 90%;
            }

            .message-input-container {
                padding: 8px 15px;
            }

            #message {
                font-size: 14px;
            }

            #send-btn {
                width: 40px;
                height: 40px;
            }
        }

        /* إضافة تأثير الكتابة */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 10px;
            background: white;
            border-radius: 20px;
            width: fit-content;
            margin: 10px;
            opacity: 0;
            animation: fadeInOut 1.5s ease infinite;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #3498db;
            border-radius: 50%;
        }

        .typing-dot:nth-child(1) { animation: bounce 1s ease infinite; }
        .typing-dot:nth-child(2) { animation: bounce 1s ease infinite 0.2s; }
        .typing-dot:nth-child(3) { animation: bounce 1s ease infinite 0.4s; }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        /* تحسينات صفحة معلومات المستخدم */
        .profile {
            background: linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%);
            padding: 30px 20px;
        }

        #profile-pic-display {
            width: 150px;
            height: 150px;
            margin: 0 auto 30px;
            position: relative;
            transform-style: preserve-3d;
            transition: transform 0.5s ease;
        }

        #profile-pic-display img,
        #profile-pic-display .default-pic {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            background: linear-gradient(45deg, #3498db, #2980b9);
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            animation: profilePicAppear 0.5s ease;
        }

        #profile-pic-display::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2));
            top: 0;
            left: 0;
            animation: shine 2s infinite;
        }

        #profile-pic-display:hover {
            transform: translateY(-5px) rotateY(10deg);
        }

        .profile h2 {
            position: relative;
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 40px;
            animation: slideDown 0.5s ease;
        }

        .profile h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 3px;
        }

        .profile-info {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 20px auto;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transform: translateY(0);
            transition: all 0.3s ease;
            animation: slideUp 0.5s ease;
        }

        .profile-info:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .info-item {
            margin: 20px 0;
            padding: 15px;
            border-radius: 15px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .info-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .info-item:hover::before {
            transform: translateX(100%);
        }

        .info-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 18px;
            color: #2c3e50;
            font-weight: 600;
        }

        #back-to-chat-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 500;
            margin-top: 30px;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            width: auto;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        #back-to-chat-btn i {
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        #back-to-chat-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
        }

        #back-to-chat-btn:hover i {
            animation: bounceLeft 0.5s ease infinite;
        }

        @keyframes profilePicAppear {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes shine {
            0% {
                opacity: 0;
                transform: translateX(-100%) rotate(45deg);
            }
            50% {
                opacity: 0.5;
            }
            100% {
                opacity: 0;
                transform: translateX(100%) rotate(45deg);
            }
        }

        @keyframes bounceLeft {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(-5px); }
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 480px) {
            #profile-pic-display {
                width: 120px;
                height: 120px;
            }

            .profile-info {
                padding: 20px;
            }

            .info-value {
                font-size: 16px;
            }
        }

        .edit-message-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            backdrop-filter: blur(8px);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .edit-message-modal.show {
            opacity: 1;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.7);
            background: linear-gradient(145deg, #ffffff, #f5f5f5);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 90%;
            max-width: 500px;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .edit-message-modal.show .modal-content {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(52, 152, 219, 0.1);
        }

        .modal-header h3 {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #2c3e50;
            font-size: 1.5rem;
            margin: 0;
        }

        .modal-icon {
            color: #3498db;
            animation: iconBounce 1s ease infinite;
        }

        .close-modal-btn {
            background: none;
            border: none;
            color: #95a5a6;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            transition: all 0.3s ease;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-modal-btn:hover {
            background: rgba(0,0,0,0.1);
            color: #e74c3c;
            transform: rotate(90deg);
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .textarea-wrapper {
            position: relative;
            margin-bottom: 20px;
        }

        #edit-message-text {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 16px;
            resize: vertical;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.9);
            color: #2c3e50;
        }

        #edit-message-text:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        .textarea-border {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #3498db, #2980b9);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        #edit-message-text:focus + .textarea-border {
            width: 100%;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .modal-buttons button {
            padding: 12px 25px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
        }

        #save-edit-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }

        #cancel-edit-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .modal-buttons button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .button-effect {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .modal-buttons button:hover .button-effect {
            transform: translateX(100%);
        }

        @keyframes iconBounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 480px) {
            .modal-content {
                padding: 20px;
                width: 95%;
            }

            .modal-buttons {
                flex-direction: column;
            }

            .modal-buttons button {
                width: 100%;
            }
        }

        /* Group Container Styles */
        .group-container {
            background: linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%);
            padding: 20px;
        }

        .group-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .group-header h2 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #2c3e50;
        }

        .group-header h2 i {
            color: #3498db;
            animation: bounceIcon 2s infinite;
        }

        .create-group-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 12px 25px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            position: relative;
        }

        .create-group-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .groups-wrapper {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }

        .group-item {
            background: linear-gradient(145deg, #ffffff, #f5f5f5);
            border-radius: 12px;
            padding: 15px 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .group-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .group-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .group-item:hover::before {
            transform: translateX(100%);
        }

        .group-item-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .group-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            transition: transform 0.3s ease;
        }

        .group-item:hover .group-icon {
            transform: scale(1.1) rotate(10deg);
        }

        .group-details {
            text-align: right;
        }

        .group-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .group-meta {
            font-size: 14px;
            color: #95a5a6;
            display: flex;
            gap: 15px;
        }

        /* Group Chat Styles */
        .group-chat-container {
            background: linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%);
        }

        .group-chat-header {
            background: white;
            padding: 15px 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .back-to-groups-btn {
            background: none;
            border: none;
            color: #95a5a6;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px;
            border-radius: 50%;
        }

        .back-to-groups-btn:hover {
            background: rgba(0,0,0,0.05);
            color: #3498db;
            transform: translateX(-3px);
        }

        .group-info {
            flex: 1;
            text-align: right;
        }

        .group-info h2 {
            margin: 0;
            color: #2c3e50;
            font-size: 20px;
        }

        .group-members-count {
            font-size: 14px;
            color: #95a5a6;
        }

        /* Password Modal Styles */
        .group-password-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            backdrop-filter: blur(8px);
            z-index: 1000;
        }

        .group-password-modal .modal-content {
            background: white;
            border-radius: 20px;
            padding: 25px;
            width: 90%;
            max-width: 400px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .password-input-wrapper {
            margin: 20px 0;
            position: relative;
        }

        #group-password-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        #group-password-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        .password-border {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #3498db, #2980b9);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        #group-password-input:focus + .password-border {
            width: 100%;
        }

        @keyframes bounceIcon {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .group-header {
                flex-direction: column;
                gap: 15px;
            }
            
            .create-group-btn {
                width: 100%;
            }
            
            .group-item {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }
            
            .group-item-info {
                flex-direction: column;
            }
            
            .group-meta {
                justify-content: center;
            }
        }

        /* تحديث أنماط دردشة المجموعات */
        .group-chat-container {
            background: linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%);
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        #group-chat-title {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .group-title-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .group-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
            color: #95a5a6;
            margin-top: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #2ecc71;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
            animation: pulse 2s infinite;
        }

        .back-to-groups-btn {
            background: none;
            border: none;
            color: #95a5a6;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: auto;
        }

        .back-to-groups-btn:hover {
            background: rgba(0,0,0,0.05);
            color: #3498db;
            transform: translateX(-3px);
        }

        .group-messages {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            overflow-y: auto;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .group-message-input {
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 80px;
        }

        .input-tools {
            display: flex;
            gap: 10px;
        }

        .tool-btn {
            background: none;
            border: none;
            color: #95a5a6;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: auto;
        }

        .tool-btn:hover {
            background: #f8f9fa;
            color: #3498db;
            transform: scale(1.1);
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .input-wrapper input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 15px;
            transition: all 0.3s ease;
        }

        .input-wrapper input:focus {
            outline: none;
            border-color: #3498db;
        }

        #send-group-message-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            padding: 0;
        }

        #send-group-message-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        #send-group-message-btn i {
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        #send-group-message-btn:hover i {
            animation: sendBounce 0.5s ease infinite;
        }

        /* تحريكات إضافية */
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.5); opacity: 0.5; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes sendBounce {
            0%, 100% { transform: translate(0); }
            50% { transform: translate(3px, -3px); }
        }

        /* تحسين عرض الرسائل في المجموعة */
        .group-messages .message {
            display: flex;
            flex-direction: column;
            max-width: 80%;
            margin: 15px 0;
            animation: messageAppear 0.3s ease;
        }

        .group-messages .message.sent {
            align-self: flex-end;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border-radius: 20px 5px 20px 20px;
        }

        .group-messages .message.received {
            align-self: flex-start;
            background: #f8f9fa;
            color: #2c3e50;
            border-radius: 5px 20px 20px 20px;
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 480px) {
            .group-title-content {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .group-meta {
                justify-content: center;
            }

            .back-to-groups-btn {
                align-self: flex-start;
            }

            .group-message-input {
                padding: 10px;
            }

            .tool-btn {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container" id="login-container">
        <h2>تسجيل الدخول</h2>
        <div class="profile-pic-upload" id="change-profile-pic">
            <i class="fas fa-user"></i>
        </div>
        <input type="file" id="profile-pic" accept="image/*">
        <input type="text" id="username" placeholder="اسم المستخدم">
        <input type="text" id="phone" placeholder="رقم الهاتف">
        <button id="login-btn">تسجيل الدخول</button>
    </div>

    <div class="home-container" id="home-container">
        <h2><i class="fas fa-home"></i> الصفحة الرئيسية</h2>
        <div class="home-buttons">
            <button class="home-btn chat-btn" id="home-chat-btn">
                <i class="fas fa-comments"></i>
                <span>الدردشة العامة</span>
                <div class="btn-effect"></div>
            </button>
            <button class="home-btn groups-btn" id="home-groups-btn">
                <i class="fas fa-users"></i>
                <span>المجموعات</span>
                <div class="btn-effect"></div>
            </button>
            <button class="home-btn search-btn" id="search-btn">
                <i class="fas fa-search"></i>
                <span>البحث عن مستخدم</span>
                <div class="btn-effect"></div>
            </button>
            <button class="home-btn requests-btn" id="mail-btn">
                <i class="fas fa-envelope"></i>
                <span>طلبات الصداقة</span>
                <div class="btn-notification">0</div>
                <div class="btn-effect"></div>
            </button>
            <button class="home-btn settings-btn" id="home-settings-btn">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
                <div class="btn-effect"></div>
            </button>
        </div>
    </div>

    <div class="chat" id="chat-container">
        <h2 id="chat-title">دردشة المعلمين</h2>
        <div class="messages" id="messages"></div>

        <div class="message-input-container">
            <div class="attachment-options">
                <label for="file-input">
                    <i class="fas fa-paperclip"></i>
                </label>
                <input type="file" id="file-input" multiple>
            </div>
            <input type="text" id="message" placeholder="اكتب رسالة">
            <button id="send-btn"><i class="fas fa-paper-plane"></i></button>
        </div>
    </div>

    <div class="settings" id="settings-container">
        <h2>الإعدادات</h2>
        <button id="logout-btn">تسجيل الخروج</button>
        <button id="change-profile-pic">تغيير صورة الملف الشخصي</button>
    </div>

    <div class="profile" id="profile-container">
        <h2><i class="fas fa-user-circle"></i> معلومات المستخدم</h2>
        <div id="profile-pic-display"></div>
        <div class="profile-info">
            <div class="info-item">
                <div class="info-label">اسم المستخدم</div>
                <div class="info-value" id="profile-name"></div>
            </div>
            <div class="info-item">
                <div class="info-label">رقم الهاتف</div>
                <div class="info-value" id="profile-phone"></div>
            </div>
        </div>
        <button id="back-to-chat-btn">
            <i class="fas fa-arrow-left"></i>
            العودة إلى الدردشة
        </button>
    </div>

    <!-- نافذة منبثقة -->
    <div class="popup" id="popup">
        <h3 id="popup-title"></h3>
        <p id="popup-info"></p>
        <button id="delete-btn">حذف الرسالة</button>
        <button id="edit-btn">تعديل الرسالة</button>
        <button id="close-popup">إغلاق</button>
    </div>

    <!-- قائمة تحكم الرسائل -->
    <div class="popup-overlay" id="popup-overlay"></div>
    <div class="message-options" id="message-options">
        <h3>إدارة الرسالة</h3>
        <div class="message-options-buttons">
            <button id="delete-message-btn">
                <i class="fas fa-trash-alt"></i>
                حذف الرسالة
            </button>
            <button id="edit-message-btn">
                <i class="fas fa-edit"></i>
                تعديل الرسالة
            </button>
            <button id="close-options">
                <i class="fas fa-times"></i>
                إغلاق
            </button>
        </div>
    </div>

    <!-- Add Group Container -->
    <div class="group-container" id="group-container">
        <div class="group-header">
            <h2>
                <i class="fas fa-users bounce-icon"></i>
                <span>المجموعات</span>
            </h2>
            <button id="create-group-btn" class="create-group-btn">
                <i class="fas fa-plus"></i>
                <span>إنشاء مجموعة جديدة</span>
                <div class="button-effect"></div>
            </button>
        </div>
        
        <div class="groups-grid-wrapper">
            <div class="groups-grid" id="group-list"></div>
        </div>
    </div>

    <!-- Group Chat Container -->
    <div class="group-chat-container" id="group-chat-container">
        <h2 id="group-chat-title">
            <div class="group-title-content">
                <button class="back-to-groups-btn" id="back-to-groups-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <span id="current-group-name">اسم المجموعة</span>
                <div class="group-meta">
                    <span class="group-members-count">
                        <i class="fas fa-users"></i>
                        <span id="group-members-count">0 عضو</span>
                    </span>
                    <span class="group-status">
                        <span class="status-dot"></span>
                        نشط الآن
                    </span>
                </div>
            </div>
        </h2>

        <div class="messages group-messages" id="group-messages"></div>

        <div class="message-input-container group-message-input">
            <div class="input-tools">
                <label for="group-file-input" class="tool-btn">
                    <i class="fas fa-paperclip"></i>
                </label>
                <button class="tool-btn">
                    <i class="far fa-smile"></i>
                </button>
            </div>
            
            <div class="input-wrapper">
                <input type="text" id="group-message" placeholder="اكتب رسالة...">
                <div class="input-border"></div>
            </div>

            <button id="send-group-message-btn">
                <i class="fas fa-paper-plane"></i>
            </button>

            <input type="file" id="group-file-input" multiple hidden>
        </div>
    </div>

    <!-- شريط التنقل -->
    <div class="navigation">
        <a href="#" class="nav-link" data-page="home-container">
            <div class="nav-icon">
                <i class="fas fa-home"></i>
                <span class="nav-tooltip">الرئيسية</span>
            </div>
            <span class="nav-text">الرئيسية</span>
        </a>
        <a href="#" class="nav-link" data-page="chat-container">
            <div class="nav-icon">
                <i class="fas fa-comments"></i>
                <span class="nav-tooltip">الدردشة</span>
            </div>
            <span class="nav-text">الدردشة</span>
        </a>
        <a href="#" class="nav-link" data-page="group-container">
            <div class="nav-icon">
                <i class="fas fa-users"></i>
                <span class="nav-tooltip">المجموعات</span>
            </div>
            <span class="nav-text">المجموعات</span>
        </a>
        <a href="#" class="nav-link" data-page="settings-container">
            <div class="nav-icon">
                <i class="fas fa-cog"></i>
                <span class="nav-tooltip">الإعدادات</span>
            </div>
            <span class="nav-text">الإعدادات</span>
        </a>
        <div class="nav-indicator"></div>
    </div>

    <!-- Search Container -->
    <div class="search-container" id="search-container">
        <h2><i class="fas fa-search"></i> البحث عن مستخدم</h2>
        <div class="search-box-wrapper">
            <div class="search-input-container">
                <input type="text" id="user-search" placeholder="اكتب اسم المستخدم للبحث...">
                <button id="search-user-btn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="loading-animation" id="loading-users">
            <div class="spinner"></div>
            <span>جاري البحث...</span>
        </div>
        <div class="user-list" id="user-list"></div>
        <button id="back-to-home" class="floating-btn">
            <i class="fas fa-arrow-left"></i>
        </button>
    </div>

    <!-- Friend Requests Container -->
    <div class="friend-requests-container" id="friend-requests-container">
        <h2>
            <i class="fas fa-user-friends"></i>
            طلبات الصداقة
        </h2>
        <div class="friend-requests-list" id="friend-requests-list">
            <div class="no-requests">
                <i class="far fa-envelope-open"></i>
                لا توجد طلبات صداقة جديدة
            </div>
        </div>
        <button id="close-friend-requests-btn">
            <i class="fas fa-times"></i>
            إغلاق
        </button>
    </div>

    <!-- إضافة مؤشر الكتابة في HTML -->
    <div class="typing-indicator">
        <span class="typing-dot"></span>
        <span class="typing-dot"></span>
        <span class="typing-dot"></span>
    </div>

    <!-- Update the edit message modal HTML -->
    <div class="edit-message-modal" id="edit-message-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>
                    <i class="fas fa-edit modal-icon"></i>
                    تعديل الرسالة
                </h3>
                <button class="close-modal-btn" id="close-modal-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="textarea-wrapper">
                    <textarea id="edit-message-text" placeholder="اكتب رسالتك هنا..."></textarea>
                    <div class="textarea-border"></div>
                </div>
            </div>
            <div class="modal-buttons">
                <button id="save-edit-btn">
                    <i class="fas fa-check"></i>
                    <span>حفظ التغييرات</span>
                    <div class="button-effect"></div>
                </button>
                <button id="cancel-edit-btn">
                    <i class="fas fa-times"></i>
                    <span>إلغاء</span>
                    <div class="button-effect"></div>
                </button>
            </div>
        </div>
    </div>

    <!-- Add Group Password Modal -->
    <div class="group-password-modal" id="group-password-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>
                    <i class="fas fa-lock"></i>
                    كلمة مرور المجموعة
                </h3>
            </div>
            <div class="modal-body">
                <div class="password-input-wrapper">
                    <input type="password" id="group-password-input" placeholder="أدخل كلمة المرور">
                    <div class="password-border"></div>
                </div>
            </div>
            <div class="modal-buttons">
                <button id="join-group-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>دخول</span>
                </button>
                <button id="cancel-join-btn">
                    <i class="fas fa-times"></i>
                    <span>إلغاء</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // ... existing code ...

        // تحديث دالة إنشاء المجموعة
        document.getElementById("create-group-btn").addEventListener("click", () => {
            const groupName = prompt("أدخل اسم المجموعة:");
            if (groupName) {
                createGroup(groupName);
            }
        });

        async function createGroup(groupName) {
            const groupId = generateGroupId();
            const groupPassword = prompt("أدخل كلمة مرور للمجموعة (اختياري):");
            
            await addDoc(collection(db, "groups"), {
                name: groupName,
                id: groupId,
                password: groupPassword || null,
                creator: localStorage.getItem("username"),
                membersCount: 1,
                members: [localStorage.getItem("username")],
                timestamp: serverTimestamp()
            });

            if (groupPassword) {
                localStorage.setItem(`group_${groupId}_access`, 'true');
            }
            
            alert("تم إنشاء المجموعة بنجاح");
        }

        // تحديث دالة تحميل المجموعات
        function loadGroups() {
            const groupList = document.getElementById("group-list");
            const q = query(collection(db, "groups"), orderBy("timestamp", "desc"));

            onSnapshot(q, (snapshot) => {
                groupList.innerHTML = "";
                snapshot.forEach((doc) => {
                    const data = doc.data();
                    const groupCard = document.createElement("div");
                    groupCard.className = "group-card";
                    
                    groupCard.innerHTML = `
                        <div class="group-card-header">
                            <div class="group-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="group-info">
                                <h3>${data.name}</h3>
                                <div class="group-meta">
                                    <span><i class="fas fa-user"></i>${data.membersCount || 1} عضو</span>
                                    <span><i class="fas fa-clock"></i>${formatDate(data.timestamp)}</span>
                                </div>
                            </div>
                        </div>
                        ${data.password ? '<i class="fas fa-lock" style="color: #95a5a6;"></i>' : ''}
                    `;
                    
                    // Add hover animation
                    groupCard.addEventListener('mouseover', () => {
                        groupCard.querySelector('.group-icon').style.transform = 'scale(1.1) rotate(10deg)';
                    });
                    
                    groupCard.addEventListener('mouseout', () => {
                        groupCard.querySelector('.group-icon').style.transform = 'scale(1) rotate(0)';
                    });
                    
                    groupCard.addEventListener('click', () => handleGroupClick(data));
                    groupList.appendChild(groupCard);
                });
            });
        }

        function formatDate(timestamp) {
            if (!timestamp) return '';
            const date = timestamp.toDate();
            return date.toLocaleDateString('ar-EG', { month: 'short', day: 'numeric' });
        }

        async function handleGroupClick(groupData) {
            const hasAccess = localStorage.getItem(`group_${groupData.id}_access`);
            
            if (groupData.creator === localStorage.getItem("username") || !groupData.password || hasAccess) {
                joinGroup(groupData);
            } else {
                showPasswordModal(groupData);
            }
        }

        function showPasswordModal(groupData) {
            const modal = document.getElementById("group-password-modal");
            const passwordInput = document.getElementById("group-password-input");
            
            modal.style.display = "block";
            passwordInput.value = "";
            passwordInput.focus();
            
            document.getElementById("join-group-btn").onclick = () => {
                if (passwordInput.value === groupData.password) {
                    localStorage.setItem(`group_${groupData.id}_access`, 'true');
                    modal.style.display = "none";
                    joinGroup(groupData);
                } else {
                    alert("كلمة المرور غير صحيحة");
                }
            };
            
            document.getElementById("cancel-join-btn").onclick = () => {
                modal.style.display = "none";
            };
        }

        async function joinGroup(groupData) {
            currentGroupId = groupData.id;
            document.getElementById("current-group-name").textContent = groupData.name;
            document.getElementById("group-members-count").textContent = 
                `${groupData.membersCount || 1} عضو`;
            
            showPage("group-chat-container");
            loadGroupMessages(groupData.id);
            
            // تحديث عدد الأعضاء إذا لم يكن المستخدم عضواً
            if (!groupData.members?.includes(localStorage.getItem("username"))) {
                await updateDoc(doc(db, "groups", groupData.id), {
                    membersCount: increment(1),
                    members: arrayUnion(localStorage.getItem("username"))
                });
            }
        }

        document.getElementById("back-to-groups-btn").addEventListener("click", () => {
            showPage("group-container");
        });

        // ... rest of the existing code ...

        function loadGroupMessages(groupId) {
            const messagesDiv = document.getElementById("group-messages");
            const q = query(collection(db, `groups/${groupId}/messages`), orderBy("timestamp"));

            onSnapshot(q, (snapshot) => {
                messagesDiv.innerHTML = "";
                snapshot.forEach((doc) => {
                    const data = doc.data();
                    const messageEl = createMessageElement(data);
                    messagesDiv.appendChild(messageEl);
                });
                // تمرير تلقائي إلى أحدث رسالة
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            });
        }

        function createMessageElement(data) {
            const isCurrentUser = data.sender === localStorage.getItem("username");
            const messageDiv = document.createElement("div");
            messageDiv.className = `message ${isCurrentUser ? 'sent' : 'received'}`;
            
            const userProfile = document.createElement("div");
            userProfile.className = "user-profile";
            userProfile.innerHTML = `
                <div class="profile-pic">${getProfilePic(data.sender)}</div>
                <div class="username">${data.sender}</div>
            `;

            const content = document.createElement("div");
            content.className = "msg-content";
            if (data.content) {
                content.innerText = data.content;
            } else if (data.fileUrl) {
                content.innerHTML = `<a href="${data.fileUrl}" target="_blank">${data.fileName}</a>`;
            }

            messageDiv.appendChild(userProfile);
            messageDiv.appendChild(content);

            // إضافة وقت الرسالة
            if (data.timestamp) {
                const timeDiv = document.createElement("div");
                timeDiv.className = "message-time";
                timeDiv.textContent = formatMessageTime(data.timestamp);
                messageDiv.appendChild(timeDiv);
            }

            return messageDiv;
        }

        function formatMessageTime(timestamp) {
            if (!timestamp) return '';
            const date = timestamp.toDate();
            return date.toLocaleTimeString('ar-EG', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        }
    </script>

</body>
</html>